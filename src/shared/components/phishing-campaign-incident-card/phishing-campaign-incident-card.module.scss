.incident-wrapper {
  align-items: center;

  background: var(--Surface, #fff);
  border: 1px solid var(--Stroke, #ebeff2);
  border-radius: 12px;

  display: flex;
  gap: 8px;
  justify-content: space-between;

  padding: 12px 24px;

  .incident-title {
    font: var(--font-title-4-normal);
  }

  .info {
    &-container{
      display: flex;
      gap: 24px;
    }
    &-period {
      display: flex;
      gap: 8px;
    }
  }

  .info-wrapper {
    align-items: center;
    display: flex;

    .info {
      align-items: center;
      display: flex;
      gap: 8px;

      &-text {
        color: var(--color-gray-70);
        font: var(--font-title-4-normal);
      }
    }
  }
}

.incident-level {
  &-GREEN {
    color: var(--color-statistics-good);
  }
  &-YELLOW {
    color: var(--color-statistics-warning);
  }
  &-RED {
    color: var(--color-statistics-bad);
  }
}

.no-break {
  white-space: nowrap;
}